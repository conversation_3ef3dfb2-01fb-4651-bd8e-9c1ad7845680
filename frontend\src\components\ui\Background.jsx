// component/VantaBackground.jsx
import React, { useEffect, useRef } from "react";
import WAVES from "vanta/dist/vanta.waves.min";
import * as THREE from "three";

export default function VantaBackground() {
  const vantaRef = useRef(null);
  const effect = useRef(null);

  useEffect(() => {
    console.log("VantaBackground useEffect triggered");
    console.log("vantaRef.current:", vantaRef.current);
    console.log("WAVES:", WAVES);
    console.log("THREE:", THREE);

    if (!effect.current && vantaRef.current) {
      try {
        console.log("Initializing Vanta WAVES effect...");
        effect.current = WAVES({
          el: vantaRef.current,
          THREE: THREE,
          mouseControls: true,
          touchControls: true,
          minHeight: 200.0,
          minWidth: 200.0,
          scale: 1.0,
          scaleMobile: 1.0,
          color: 0x1e40af,
          shininess: 50,
          waveHeight: 20,
          waveSpeed: 1.0,
        });
        console.log("Vanta effect created:", effect.current);
      } catch (error) {
        console.error("Error creating Vanta effect:", error);
      }
    }
    return () => {
      if (effect.current) {
        console.log("Destroying Vanta effect");
        effect.current.destroy();
        effect.current = null;
      }
    };
  }, []);

  return (
    <div
      ref={vantaRef}
      className="fixed inset-0 w-full h-full"
      style={{
        zIndex: 1,
        pointerEvents: 'none'
      }}
    />
  );
}
